# Bookiime Frontend

A modern booking and service management platform built with Nuxt 3, Vue 3, and TypeScript.

## Features

- 🎯 **Service Management** - Create and manage services with pricing, duration, and availability
- 📅 **Event Management** - Organize events with date/time, location, and attendee limits
- 🔐 **Authentication** - Secure user authentication with JWT tokens
- 📱 **Responsive Design** - Mobile-first design with Tailwind CSS
- 🎨 **Modern UI** - Built with shadcn/ui components and Lucide icons
- ⚡ **Performance** - Optimized with Nuxt 3 and Vue 3 Composition API

## Tech Stack

- **Framework**: Nuxt 3
- **Frontend**: Vue 3 with TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui (Reka UI)
- **Icons**: Lucide Vue Next
- **State Management**: Nuxt built-in state management
- **HTTP Client**: Nuxt $fetch with custom composables

## Prerequisites

- Node.js 18+
- npm, pnpm, yarn, or bun

## Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd bookiime-frontend
   ```

2. **Install dependencies**

   ```bash
   # npm
   npm install

   # pnpm
   pnpm install

   # yarn
   yarn install

   # bun
   bun install
   ```

3. **Environment Configuration**

   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your configuration:

   ```env
   NUXT_PUBLIC_API_BASE=http://localhost:8000/api
   NUXT_PUBLIC_AUTH_COOKIE_NAME=bookiime_auth_token
   NUXT_PUBLIC_USER_COOKIE_NAME=bookiime_user_data
   PORT=3000
   ```

## Development

Start the development server:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

The application will be available at `http://localhost:3000`

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Preview the production build locally:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## Project Structure

```
├── assets/          # Static assets (CSS, images)
├── components/      # Vue components
│   ├── app/        # App-level components
│   ├── auth/       # Authentication components
│   ├── dashboard/  # Dashboard components
│   ├── services/   # Service management components
│   └── ui/         # Reusable UI components
├── composables/     # Vue composables
├── layouts/         # Nuxt layouts
├── middleware/      # Route middleware
├── pages/          # File-based routing
├── plugins/        # Nuxt plugins
├── public/         # Public static files
├── server/         # Server-side code
├── types/          # TypeScript type definitions
└── utils/          # Utility functions
```

## Key Features

### Authentication

- JWT-based authentication with secure cookie storage
- Protected routes with middleware
- User session management

### Service Management

- Create, edit, and delete services
- Image upload support
- Category management
- Pricing and duration configuration

### Event Management

- Event creation with date/time selection
- Location and attendee management
- Event categorization
- Image upload support

### UI/UX

- Responsive design for all screen sizes
- Dark/light mode support (via Tailwind)
- Loading states and error handling
- Toast notifications

## API Integration

The frontend integrates with a backend API using custom composables:

- `$apiFetch` - Unified API client with authentication
- `useApiFetch` - Reactive API calls with Nuxt's useFetch
- Automatic token management and error handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.
#   b o o k i i m e - f r o n t e n d 
 
 
