<template>
    <div class="p-6">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-h4 font-bold text-neutral-900">{{ showEvents ? 'Events' : 'Services' }}</h1>
          <p class="text-neutral-600 mt-1">{{ showEvents ? 'Manage your events and activities' : 'Manage your services' }}</p>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Toggle between Services and Events -->
          <div class="flex bg-neutral-100 rounded-lg p-1">
            <button @click="showEvents = false" :class="toggleButtonClass(false)">
              <Icon name="lucide:briefcase" class="w-4 h-4" />
              <span>Services</span>
            </button>
            <button @click="showEvents = true" :class="toggleButtonClass(true)">
              <Icon name="lucide:calendar" class="w-4 h-4" />
              <span>Events</span>
            </button>
          </div>

          <!-- Create Dialog Trigger -->
          <Dialog v-model:open="isCreateOpen">
            <DialogTrigger as-child>
              <button
                class="px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white shadow-md transition"
              >
                + {{ showEvents ? 'Create Event' : 'Create Service' }}
              </button>
            </DialogTrigger>

            <!-- Create Dialog Content -->
            <DialogContent
              class="w-full sm:max-w-[720px] max-h-[80vh] overflow-y-auto"
            >
              <DialogHeader>
                <DialogTitle>{{ showEvents ? 'Create Event' : 'Create Service' }}</DialogTitle>
                <DialogDescription>
                  Fill in the details to create a new {{ showEvents ? 'event' : 'service' }}.
                </DialogDescription>
              </DialogHeader>

              <form @submit.prevent="createItem" class="space-y-4 mt-4 pb-6">
                <!-- Service Form Fields -->
                <template v-if="!showEvents">
                  <InputField
                    v-model="form.name"
                    label="Service Name"
                    required
                    placeholder="Enter service name"
                  />
                  <InputTextarea
                    v-model="form.description"
                    label="Description"
                    placeholder="Describe your service"
                  />
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      v-model="priceStr"
                      label="Price (₵)"
                      type="number"
                      step="0.01"
                      required
                      placeholder="0.00"
                    />
                    <InputField
                      v-model="durationStr"
                      label="Duration (minutes)"
                      type="number"
                      required
                      placeholder="60"
                    />
                  </div>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="flex items-center space-x-3">
                      <Switch
                        v-model:checked="form.isActive"
                        id="isActive"
                      />
                      <label for="isActive" class="text-sm font-medium text-neutral-700">
                        Active
                      </label>
                    </div>
                    <div class="flex items-center space-x-3">
                      <Switch
                        v-model:checked="form.allowOverlap"
                        id="allowOverlap"
                      />
                      <label for="allowOverlap" class="text-sm font-medium text-neutral-700">
                        Allow Overlap
                      </label>
                    </div>
                  </div>
                </template>

                <!-- Event Form Fields -->
                <template v-else>
                  <InputField
                    v-model="eventForm.title"
                    label="Event Title"
                    required
                    placeholder="Enter event title"
                  />
                  <InputTextarea
                    v-model="eventForm.description"
                    label="Description"
                    placeholder="Describe your event"
                  />
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      v-model="eventForm.startDate"
                      label="Start Date"
                      type="date"
                      required
                    />
                    <InputField
                      v-model="eventForm.endDate"
                      label="End Date"
                      type="date"
                      required
                    />
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      v-model="eventForm.startTime"
                      label="Start Time"
                      type="time"
                      required
                    />
                    <InputField
                      v-model="eventForm.endTime"
                      label="End Time"
                      type="time"
                      required
                    />
                  </div>
                  <InputField
                    v-model="eventForm.location"
                    label="Location"
                    placeholder="Event location"
                  />
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <InputField
                      v-model="eventPriceStr"
                      label="Price (optional)"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                    />
                    <InputField
                      v-model="maxAttendeesStr"
                      label="Max Attendees (optional)"
                      type="number"
                      placeholder="100"
                    />
                  </div>
                  <div class="flex items-center space-x-3">
                    <Switch
                      v-model:checked="eventForm.isActive"
                      id="eventIsActive"
                    />
                    <label for="eventIsActive" class="text-sm font-medium text-neutral-700">
                      Active
                    </label>
                  </div>
                </template>

                <!-- Image Upload -->
                <div>
                  <label class="block text-body-3 font-medium text-neutral-700 mb-2">
                    {{ showEvents ? 'Event' : 'Service' }} Image
                  </label>
                  <input
                    type="file"
                    @change="handleFile"
                    accept="image/*"
                    class="w-full mt-2 border border-neutral-300 rounded-lg p-2"
                  />
                  <div
                    v-if="imageFileName"
                    class="text-sm text-neutral-600 mt-2"
                  >
                    {{ imageFileName }}
                  </div>
                </div>

                <div class="flex justify-end gap-3 mt-2">
                  <DialogClose as-child>
                    <button
                      type="button"
                      class="px-4 py-2 bg-neutral-200 rounded-lg"
                    >
                      Cancel
                    </button>
                  </DialogClose>
                  <button
                    type="submit"
                    class="px-4 py-2 rounded-lg bg-primary-500 hover:bg-primary-600 text-white shadow-md"
                    :disabled="loading"
                  >
                    {{ loading ? "Creating..." : `Create ${showEvents ? 'Event' : 'Service'}` }}
                  </button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <!-- Data Table -->
      <DataTable
        :columns="currentColumns"
        :data="currentItems"
      />

      <!-- VIEW DIALOG -->
      <Dialog v-model:open="isViewOpen">
        <DialogContent
          class="w-full sm:max-w-[600px] max-h-[80vh] overflow-y-auto"
        >
          <DialogHeader>
            <DialogTitle>{{ showEvents ? 'Event' : 'Service' }} Details</DialogTitle>
          </DialogHeader>

          <div class="space-y-3 mt-3">
            <template v-if="!showEvents && selectedItem">
              <div>
                <strong>Name:</strong> {{ (selectedItem as Service)?.name }}
              </div>
              <div>
                <strong>Description:</strong> {{ selectedItem?.description || "-" }}
              </div>
              <div>
                <strong>Price:</strong>
                {{ (selectedItem as Service)?.price != null ? `₵${Number((selectedItem as Service).price).toFixed(2)}` : '-' }}
              </div>
              <div>
                <strong>Duration:</strong> {{ (selectedItem as Service)?.duration }} minutes
              </div>
              <div>
                <strong>Status:</strong> {{ (selectedItem as Service)?.isActive ? 'Active' : 'Inactive' }}
              </div>
              <div>
                <strong>Allow Overlap:</strong> {{ (selectedItem as Service)?.allowOverlap ? 'Yes' : 'No' }}
              </div>
            </template>
            <template v-else-if="showEvents && selectedItem">
              <div>
                <strong>Title:</strong> {{ (selectedItem as Event)?.title }}
              </div>
              <div>
                <strong>Description:</strong> {{ selectedItem?.description || "-" }}
              </div>
              <div>
                <strong>Start Date:</strong> {{ formatDate((selectedItem as Event)?.startDate) }}
              </div>
              <div>
                <strong>End Date:</strong> {{ formatDate((selectedItem as Event)?.endDate) }}
              </div>
              <div>
                <strong>Time:</strong> {{ (selectedItem as Event)?.startTime }} - {{ (selectedItem as Event)?.endTime }}
              </div>
              <div>
                <strong>Location:</strong> {{ (selectedItem as Event)?.location || "-" }}
              </div>
              <div>
                <strong>Price:</strong>
                {{ (selectedItem as Event)?.price != null ? `₵${Number((selectedItem as Event).price).toFixed(2)}` : 'Free' }}
              </div>
              <div v-if="(selectedItem as Event)?.maxAttendees">
                <strong>Max Attendees:</strong> {{ (selectedItem as Event)?.maxAttendees }}
              </div>
              <div>
                <strong>Status:</strong> {{ (selectedItem as Event)?.isActive ? 'Active' : 'Inactive' }}
              </div>
            </template>
          </div>

          <div class="mt-4 flex justify-end">
            <DialogClose as-child>
              <button class="px-4 py-2 bg-neutral-200 rounded-lg">Close</button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      <!-- DELETE CONFIRM -->
      <Dialog v-model:open="isDeleteOpen">
        <DialogContent class="w-full sm:max-w-[420px]">
          <DialogHeader>
            <DialogTitle>Delete {{ showEvents ? 'Event' : 'Service' }}</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this {{ showEvents ? 'event' : 'service' }}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <div class="mt-4 flex justify-end gap-3">
            <DialogClose as-child>
              <button class="px-4 py-2 bg-neutral-200 rounded-lg">
                Cancel
              </button>
            </DialogClose>
            <button
              @click="confirmDelete"
              class="px-4 py-2 rounded-lg bg-red-600 text-white"
              :disabled="deleteLoading"
            >
              {{ deleteLoading ? "Deleting..." : "Delete" }}
            </button>
          </div>
        </DialogContent>
      </Dialog>


    </div>
</template>

<script setup lang="ts">
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button'
import { switch as Switch } from '@/components/ui/switch'
import DataTable from '@/components/data-table/data-table.vue'
import type { ColumnDef } from '@tanstack/vue-table'
import type { Service, ServiceFormData, Event, EventFormData } from '@/types/service.types'
import { $toast } from '~/composables/useToast'
import { $apiFetch } from '~/composables/apiFetch'
import { h } from 'vue'

definePageMeta({ layout: 'dashboard' })
useHead({
  title: 'Services & Events - Bookiime',
  meta: [{ name: 'description', content: 'Manage your services, events and offerings' }]
})

// State
const showEvents = ref(false)
const loading = ref(false)
const editLoading = ref(false)
const deleteLoading = ref(false)

const isCreateOpen = ref(false)
const isViewOpen = ref(false)
const isEditOpen = ref(false)
const isDeleteOpen = ref(false)

const selectedItem = ref<Service | Event | null>(null)

// Fetch data using useApiFetch
const {
  data: servicesResp,
  pending: servicesPending,
  refresh: refreshServices,
} = useApiFetch<Service[]>("/services");

const {
  data: eventsResp,
  pending: eventsPending,
  refresh: refreshEvents,
} = useApiFetch<Event[]>("/events");

const services = computed(() => servicesResp?.value ?? []);
const events = computed(() => eventsResp?.value ?? []);

// Form data for creating/editing
const form = reactive({
  name: "",
  description: "",
  price: 0,
  duration: 60,
  isActive: true,
  allowOverlap: false,
});

const eventForm = reactive({
  title: "",
  description: "",
  startDate: "",
  endDate: "",
  startTime: "",
  endTime: "",
  location: "",
  category: "",
  isActive: true,
  maxAttendees: undefined as number | undefined,
  price: undefined as number | undefined,
});

const imageFile = ref<File | null>(null);
const imageFileName = ref("");

// Computed string representations for number inputs
const priceStr = computed({
  get: () => form.price ? String(form.price) : "",
  set: (v: string) => form.price = v ? Number(v) : 0,
});

const durationStr = computed({
  get: () => form.duration ? String(form.duration) : "",
  set: (v: string) => form.duration = v ? Number(v) : 60,
});

const eventPriceStr = computed({
  get: () => eventForm.price ? String(eventForm.price) : "",
  set: (v: string) => eventForm.price = v ? Number(v) : undefined,
});

const maxAttendeesStr = computed({
  get: () => eventForm.maxAttendees ? String(eventForm.maxAttendees) : "",
  set: (v: string) => eventForm.maxAttendees = v ? Number(v) : undefined,
});

// Define columns for services
const serviceColumns: ColumnDef<Service>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const desc = row.original.description;
      return desc && desc.length > 50 ? desc.substring(0, 50) + '...' : desc || '-';
    },
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.price;
      return price != null ? `₵${Number(price).toFixed(2)}` : '-';
    },
  },
  {
    accessorKey: 'duration',
    header: 'Duration',
    cell: ({ row }) => `${row.original.duration} min`,
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => row.original.isActive ? 'Active' : 'Inactive',
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const service = row.original
      return h('div', { class: 'flex gap-2' }, [
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openView(service),
        }, () => 'View'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openEdit(service),
        }, () => 'Edit'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          class: 'text-red-600',
          onClick: () => openDelete(service),
        }, () => 'Delete'),
      ])
    },
  },
];

// Define columns for events
const eventColumns: ColumnDef<Event>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const desc = row.original.description;
      return desc && desc.length > 50 ? desc.substring(0, 50) + '...' : desc || '-';
    },
  },
  {
    accessorKey: 'startDate',
    header: 'Start Date',
    cell: ({ row }) => formatDate(row.original.startDate),
  },
  {
    accessorKey: 'startTime',
    header: 'Time',
    cell: ({ row }) => `${row.original.startTime} - ${row.original.endTime}`,
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => row.original.location || '-',
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.price;
      return price != null ? `₵${Number(price).toFixed(2)}` : 'Free';
    },
  },
  {
    accessorKey: 'isActive',
    header: 'Status',
    cell: ({ row }) => row.original.isActive ? 'Active' : 'Inactive',
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const event = row.original
      return h('div', { class: 'flex gap-2' }, [
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openView(event),
        }, () => 'View'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          onClick: () => openEdit(event),
        }, () => 'Edit'),
        h(Button, {
          variant: 'outline',
          size: 'sm',
          class: 'text-red-600',
          onClick: () => openDelete(event),
        }, () => 'Delete'),
      ])
    },
  },
];

// Computed properties
const currentColumns = computed(() => showEvents.value ? eventColumns : serviceColumns);
const currentItems = computed(() => showEvents.value ? events.value : services.value);

// Helper functions
function handleFile(e: Event) {
  const input = e.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    imageFile.value = input.files[0];
    imageFileName.value = input.files[0].name;
  }
}

function resetForm() {
  form.name = "";
  form.description = "";
  form.price = 0;
  form.duration = 60;
  form.isActive = true;
  form.allowOverlap = false;

  eventForm.title = "";
  eventForm.description = "";
  eventForm.startDate = "";
  eventForm.endDate = "";
  eventForm.startTime = "";
  eventForm.endTime = "";
  eventForm.location = "";
  eventForm.category = "";
  eventForm.isActive = true;
  eventForm.maxAttendees = undefined;
  eventForm.price = undefined;

  imageFile.value = null;
  imageFileName.value = "";
}

function formatDate(dateStr?: string) {
  if (!dateStr) return "-";
  try {
    return new Date(dateStr).toLocaleDateString();
  } catch {
    return dateStr;
  }
}

const toggleButtonClass = (isEvents: boolean) => [
  'flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
  showEvents.value === isEvents ? 'bg-white text-primary-600 shadow-sm' : 'text-neutral-600 hover:text-neutral-900'
];

// CRUD Functions
async function createItem() {
  loading.value = true;
  try {
    const fd = new FormData();

    if (showEvents.value) {
      // Create event
      fd.append("title", eventForm.title);
      fd.append("description", eventForm.description);
      fd.append("startDate", eventForm.startDate);
      fd.append("endDate", eventForm.endDate);
      fd.append("startTime", eventForm.startTime);
      fd.append("endTime", eventForm.endTime);
      fd.append("isActive", eventForm.isActive.toString());

      if (eventForm.location) fd.append("location", eventForm.location);
      if (eventForm.category) fd.append("category", eventForm.category);
      if (eventForm.maxAttendees) fd.append("maxAttendees", eventForm.maxAttendees.toString());
      if (eventForm.price) fd.append("price", eventForm.price.toString());
      if (imageFile.value) fd.append("image", imageFile.value);

      await $apiFetch("/events", fd);
      await refreshEvents();
    } else {
      // Create service
      fd.append("name", form.name);
      fd.append("description", form.description);
      fd.append("price", form.price.toString());
      fd.append("duration", form.duration.toString());
      fd.append("isActive", form.isActive.toString());
      fd.append("allowOverlap", form.allowOverlap.toString());
      if (imageFile.value) fd.append("image", imageFile.value);

      await $apiFetch("/services", fd);
      await refreshServices();
    }

    resetForm();
    isCreateOpen.value = false;
    $toast(`${showEvents.value ? 'Event' : 'Service'} created successfully`, { type: 'success' });
  } catch (err) {
    console.error(err);
    $toast(`Failed to create ${showEvents.value ? 'event' : 'service'}`, { type: 'error' });
  } finally {
    loading.value = false;
  }
}

// View, Edit, Delete functions
function openView(item: Service | Event) {
  selectedItem.value = item;
  isViewOpen.value = true;
}

function openEdit(item: Service | Event) {
  selectedItem.value = item;
  // TODO: Populate edit form and open edit dialog
  isEditOpen.value = true;
}

function openDelete(item: Service | Event) {
  selectedItem.value = item;
  isDeleteOpen.value = true;
}

async function confirmDelete() {
  if (!selectedItem.value) return;
  deleteLoading.value = true;
  try {
    if (showEvents.value) {
      await $apiFetch(`/events/${selectedItem.value.id}`, undefined, { method: "DELETE" });
      await refreshEvents();
    } else {
      await $apiFetch(`/services/${selectedItem.value.id}`, undefined, { method: "DELETE" });
      await refreshServices();
    }
    isDeleteOpen.value = false;
    $toast(`${showEvents.value ? 'Event' : 'Service'} deleted successfully`, { type: 'success' });
  } catch (err) {
    console.error(err);
    $toast(`Failed to delete ${showEvents.value ? 'event' : 'service'}`, { type: 'error' });
  } finally {
    deleteLoading.value = false;
  }
}

</script>